我的问题是：

## 数据分析问题（基于2010-2012年零售数据）

**注意：所有问题均需要多表关联查询，答案必须明确具体**

| 问题编号 | 问题描述 |
|---------|---------|
| 1 | 列出2012年第二季度（4-6月），销售额排名前5的商店及其对应的规模和类型 |
| 2 | 2012年A类型商店在假日的销售额占该类型商店总销售额的比例是多少？ |
| 3 | 列出2012年所有B类型商店中，销售额占比最高的部门及其具体销售额 |
| 4 | 2012年相比2011年，哪个类型的商店假日销售额同比增长率最高？具体增长率是多少？ |
| 5 | 列出2012年所有大规模商店（规模>150000）中，销售额同比增长率最低的部门及其销售额 |
| 6 | 2012年相比2011年，哪家A类型商店的假日销售额同比增长率最低？具体增长率是多少？ |
| 7 | 列出2012年所有小规模商店（规模<100000）中，销售额同比增长率最高的部门及其销售额 |

## 数据集说明

本项目包含三个CSV数据文件：

1. **sales data-set.csv** - 销售数据表
   - 商店：商店编号（1-45）
   - 部门：部门编号（1-99）
   - 日期：销售日期（2010年2月-2012年10月）
   - 销售额：具体销售金额
   - 是否为假日：TRUE/FALSE标识

2. **stores data-set.csv** - 商店信息表
   - 商店：商店编号（与销售表关联）
   - 类型：商店类型（A/B）
   - 规模：商店规模数值

3. **Features data set.csv** - 特征数据表
   - 商店：商店编号
   - 日期：日期
   - 温度、燃料价格、消费者物价指数等环境特征

## 技术要求

1. **编程语言**：使用Python进行数据分析
2. **必须多表关联**：每个问题都必须使用JOIN操作关联至少两个表
3. **数据处理**：使用pandas库进行CSV文件读取和处理
4. **输出格式**：所有结果必须以Markdown格式的DataFrame输出，使用 `df.to_markdown()` 方法
5. **答案明确性**：每个问题的答案必须是具体的数值、排名或列表

## 关联关系说明

- **销售表 ← → 商店表**：通过"商店"字段关联
- **销售表 ← → 特征表**：通过"商店"和"日期"字段关联
- **时间范围**：2011年和2012年的同期对比分析
- **季度定义**：Q2为4-6月，Q3为7-9月，Q4为10-12月

## 输出要求

1. 每个Python文件应包含完整的数据处理流程
2. 必须显示JOIN操作的过程
3. 结果必须使用 `print(df.to_markdown(index=False))` 输出为Markdown格式
4. 对于排名类问题，输出应包含排序后的完整结果
5. 对于占比类问题，输出应包含具体的百分比数值
6. 代码应包含适当的注释说明处理逻辑
